import * as multisig from '@sqds/multisig'
import { Connection, PublicKey, SystemProgram, TransactionMessage } from '@solana/web3.js'
import { MULTISIG_ADDRESS_PUBKEY, SQUADS_PROGRAM_ID_V4_PUBKEY, SOLANA_RPC_URL } from './constants.js'

const connection = new Connection(SOLANA_RPC_URL)

async function testMemoCreation() {
  try {
    console.log('Testing how memo is stored in Squads transactions...')
    
    // 获取多签账户信息
    const { transactionIndex } = await multisig.accounts.Multisig.fromAccountAddress(connection, MULTISIG_ADDRESS_PUBKEY)
    console.log('Latest transaction index:', Number(transactionIndex))
    
    // 模拟创建一个带 memo 的交易指令（不实际发送）
    const nextTransactionIndex = BigInt(transactionIndex) + 1n
    const testMemo = "Test memo for debugging"
    
    // 创建一个简单的转账指令作为测试
    const testTransferInstruction = SystemProgram.transfer({
      fromPubkey: new PublicKey("11111111111111111111111111111111"),
      toPubkey: new PublicKey("11111111111111111111111111111111"),
      lamports: 1000000,
    })
    
    const { blockhash } = await connection.getLatestBlockhash()
    const transferMessage = new TransactionMessage({ 
      payerKey: new PublicKey("11111111111111111111111111111111"), 
      recentBlockhash: blockhash, 
      instructions: [testTransferInstruction] 
    })
    
    // 创建 vaultTransactionCreate 指令
    const createIx = multisig.instructions.vaultTransactionCreate({
      multisigPda: MULTISIG_ADDRESS_PUBKEY,
      transactionIndex: nextTransactionIndex,
      creator: new PublicKey("EMXYq4vz4QEyxj63gerfCGjxGBrBFc7gfoiNdpf85RiG"), // 使用已知的创建者
      vaultIndex: 0,
      ephemeralSigners: 0,
      transactionMessage: transferMessage,
      memo: testMemo,
      programId: SQUADS_PROGRAM_ID_V4_PUBKEY,
    })
    
    console.log('VaultTransactionCreate instruction created successfully')
    console.log('Instruction keys count:', createIx.keys.length)
    console.log('Instruction data length:', createIx.data.length)
    
    // 检查指令数据中是否包含 memo
    const dataString = createIx.data.toString('utf8')
    console.log('Instruction data (first 200 chars):', dataString.substring(0, 200))
    
    if (dataString.includes(testMemo)) {
      console.log('✅ Memo found in instruction data!')
    } else {
      console.log('❌ Memo not found in instruction data')
      
      // 尝试以 hex 格式查看数据
      console.log('Instruction data (hex):', createIx.data.toString('hex'))
      
      // 检查是否 memo 以其他方式编码
      const memoBuffer = Buffer.from(testMemo, 'utf8')
      if (createIx.data.includes(memoBuffer)) {
        console.log('✅ Memo found as UTF-8 buffer in instruction data!')
      }
    }
    
    // 检查 Squads SDK 版本和可用方法
    console.log('\nSquads SDK methods available:')
    console.log('multisig.instructions methods:', Object.keys(multisig.instructions))
    console.log('multisig.accounts methods:', Object.keys(multisig.accounts))
    
  } catch (error) {
    console.error('Error testing memo creation:', error.message)
    console.error('Stack trace:', error.stack)
  }
}

testMemoCreation()
