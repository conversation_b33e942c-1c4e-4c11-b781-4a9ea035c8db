import * as multisig from '@sqds/multisig'
import { Connection, PublicKey } from '@solana/web3.js'
import { MULTISIG_ADDRESS_PUBKEY, SQUADS_PROGRAM_ID_V4_PUBKEY, SOLANA_RPC_URL } from './constants.js'

const connection = new Connection(SOLANA_RPC_URL)

async function testMemoExtraction() {
  try {
    console.log('Testing Memo extraction from Squads transactions...')

    // 获取多签账户信息
    const { transactionIndex } = await multisig.accounts.Multisig.fromAccountAddress(connection, MULTISIG_ADDRESS_PUBKEY)
    console.log('Latest transaction index:', Number(transactionIndex))

    // 测试最近的几个交易
    for (let i = Math.max(1, Number(transactionIndex) - 5); i <= Number(transactionIndex); i++) {
      console.log(`\n--- Testing Transaction #${i} ---`)

      // 检查 proposal 是否存在
      const [proposalPda] = multisig.getProposalPda({
        multisigPda: MULTISIG_ADDRESS_PUBKEY,
        transactionIndex: i,
        programId: SQUADS_PROGRAM_ID_V4_PUBKEY
      })

      const proposalAccount = await connection.getAccountInfo(proposalPda)

      if (!proposalAccount) {
        console.log(`Transaction #${i}: No proposal data`)
        continue
      }

      // 检查 transaction 数据
      const [transactionPda] = multisig.getTransactionPda({
        multisigPda: MULTISIG_ADDRESS_PUBKEY,
        index: BigInt(i),
        programId: SQUADS_PROGRAM_ID_V4_PUBKEY
      })

      const transactionAccount = await connection.getAccountInfo(transactionPda)

      if (!transactionAccount) {
        console.log(`Transaction #${i}: No transaction data`)
        continue
      }

      try {
        // 尝试解析 VaultTransaction
        const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0]
        console.log(`Transaction #${i} VaultTransaction fields:`, Object.keys(vaultTransactionData))

        // 检查是否有 memo 字段
        if (vaultTransactionData.memo !== undefined) {
          console.log(`Transaction #${i} memo:`, vaultTransactionData.memo)
        } else {
          console.log(`Transaction #${i}: No memo field found in VaultTransaction`)
        }

        // 显示其他字段
        console.log(`Transaction #${i} creator:`, vaultTransactionData.creator.toBase58())

        if (vaultTransactionData.message) {
          console.log(`Transaction #${i} message fields:`, Object.keys(vaultTransactionData.message))
          console.log(`Transaction #${i} instructions count:`, vaultTransactionData.message.instructions.length)

          // 检查指令中是否有 memo 程序
          vaultTransactionData.message.instructions.forEach((instruction, idx) => {
            const programId = vaultTransactionData.message.accountKeys[instruction.programIdIndex]
            console.log(`Transaction #${i} instruction ${idx} programId:`, programId.toBase58())

            // 检查是否是 Memo 程序 (MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr)
            if (programId.toBase58() === 'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr' ||
                programId.toBase58() === 'Memo1UhkJRfHyvLMcVucJwxXeuD728EqVDDwQDxFMNo') {
              console.log(`Transaction #${i} Found Memo instruction!`)
              console.log(`Transaction #${i} Memo data:`, Buffer.from(instruction.data).toString('utf8'))
            }
          })
        }

      } catch (error) {
        console.log(`Transaction #${i}: Failed to parse VaultTransaction:`, error.message)

        // 尝试其他可能的账户类型
        try {
          const configTransactionData = multisig.accounts.ConfigTransaction.fromAccountInfo(transactionAccount)[0]
          console.log(`Transaction #${i} ConfigTransaction fields:`, Object.keys(configTransactionData))

          if (configTransactionData.memo !== undefined) {
            console.log(`Transaction #${i} config memo:`, configTransactionData.memo)
          }
        } catch (configError) {
          console.log(`Transaction #${i}: Also failed to parse as ConfigTransaction:`, configError.message)
        }
      }
    }

  } catch (error) {
    console.error('Error testing memo extraction:', error.message)
    console.error('Stack trace:', error.stack)
  }
}

testMemoExtraction()
